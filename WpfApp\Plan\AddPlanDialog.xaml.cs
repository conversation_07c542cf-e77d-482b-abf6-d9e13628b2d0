using System.Windows.Controls;

namespace WpfApp.Plan
{
    /// <summary>
    /// AddPlanDialog.xaml 的交互逻辑
    /// </summary>
    public partial class AddPlanDialog : UserControl
    {
        public AddPlanDialog()
        {
            InitializeComponent();
            // 不要设置 DataContext，让它继承父级
        }

        private void UploadAttachment_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择附件文件",
                Filter = "支持的文件类型|*.docx;*.xls;*.xlsx;*.pdf;*.rar;*.zip;*.png;*.jpg;*.jpeg|所有文件|*.*",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                // 这里假设DataContext就是PlanViewModel
                if (this.DataContext is PlanViewModel vm)
                {
                    foreach (string fileName in openFileDialog.FileNames)
                    {
                        string fileNameOnly = System.IO.Path.GetFileName(fileName);
                        vm.Attachments.Add(fileNameOnly);
                    }
                }
            }
        }
    }
} 