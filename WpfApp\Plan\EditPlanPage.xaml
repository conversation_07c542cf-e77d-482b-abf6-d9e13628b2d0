<UserControl x:Class="WpfApp.Plan.EditPlanPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WpfApp.Plan"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d">
    <Grid Background="#F5F5F5">
        <!-- 页面标题 -->
        <TextBlock Text="新增计划" FontSize="22" FontWeight="Bold" Margin="20,20,0,0" VerticalAlignment="Top" HorizontalAlignment="Left"/>

        <!-- 主要内容 - 采用垂直StackPanel布局 -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="0,60,0,0">
            <StackPanel Margin="20,0,20,20">
                <!-- 导航标签 -->
                <Border Background="White" BorderThickness="0" CornerRadius="4" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <Border Background="#F7F9FC" BorderThickness="0" CornerRadius="2">
                            <Button Content="基础信息" Background="Transparent" BorderThickness="0" HorizontalAlignment="Center" 
                                    Foreground="#3080FE" Padding="40,10" FontWeight="Bold" FontSize="16"/>
                        </Border>
                    </StackPanel>
                </Border>
                
                <!-- 基础信息区域 -->
                <Border Background="White" BorderThickness="0" CornerRadius="4" Margin="0,0,0,20" Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 计划编号 -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="计划编号" Foreground="#303133" FontSize="14" Margin="2,0,0,0"/>
                            </StackPanel>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <TextBox Text="{Binding PlanModel.PlanCode}" 
                                        IsReadOnly="True"
                                        BorderThickness="0" 
                                        Padding="10,8" 
                                        FontSize="14" 
                                        materialDesign:HintAssist.Hint="请输入"
                                        Foreground="#606266"/>
                            </Border>
                        </StackPanel>
                        
                        <!-- 计划名称 -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="计划名称" Foreground="#303133" FontSize="14" Margin="2,0,0,0"/>
                            </StackPanel>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <TextBox Text="{Binding PlanModel.PlanName}" 
                                        BorderThickness="0" 
                                        Padding="10,8" 
                                        FontSize="14" 
                                        materialDesign:HintAssist.Hint="请输入"
                                        Foreground="#606266"/>
                            </Border>
                        </StackPanel>

                        <!-- 来源类型 -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,15">
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="*" Foreground="Red" FontSize="14"/>
                                <TextBlock Text="来源类型" Foreground="#303133" FontSize="14" Margin="2,0,0,0"/>
                            </StackPanel>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <ComboBox ItemsSource="{Binding SourceTypes}" 
                                        SelectedItem="{Binding SelectedSourceType}"
                                        DisplayMemberPath="Name"
                                        BorderThickness="0" 
                                        Padding="8,6" 
                                        FontSize="14" 
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="销售订单">
                                    <ComboBox.Resources>
                                        <SolidColorBrush x:Key="MaterialDesignPaper" Color="White"/>
                                    </ComboBox.Resources>
                                </ComboBox>
                            </Border>
                        </StackPanel>
                        
                        <!-- 订单编号 -->
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,15">
                            <TextBlock Text="订单编号" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <Grid>
                                    <TextBox Text="{Binding PlanModel.OrderId}" 
                                            BorderThickness="0" 
                                            Padding="10,8" 
                                            FontSize="14" 
                                            materialDesign:HintAssist.Hint="请选择销售单"
                                            IsReadOnly="True"
                                            Foreground="#606266"/>
                                    <Button HorizontalAlignment="Right" Width="30" Height="30" Padding="0" Margin="0,0,5,0"
                                            Background="Transparent" BorderThickness="0">
                                        <materialDesign:PackIcon Kind="ViewGrid" Foreground="#409EFF" Width="16" Height="16"/>
                                    </Button>
                                </Grid>
                            </Border>
                        </StackPanel>

                        <!-- 成品名称 -->
                        <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,15">
                            <TextBlock Text="成品名称" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <Grid>
                                    <TextBox Text="{Binding PlanModel.ProductName}" 
                                            IsReadOnly="True"
                                            BorderThickness="0" 
                                            Padding="10,8" 
                                            FontSize="14" 
                                            materialDesign:HintAssist.Hint="请选择成品"
                                            Foreground="#606266"/>
                                    <Button HorizontalAlignment="Right" Width="30" Height="30" Padding="0" Margin="0,0,5,0"
                                            Command="{Binding SelectProductCommand}"
                                            Background="Transparent" BorderThickness="0">
                                        <materialDesign:PackIcon Kind="ViewGrid" Foreground="#409EFF" Width="16" Height="16"/>
                                    </Button>
                                </Grid>
                            </Border>
                        </StackPanel>
                        
                        <!-- 成品编号 -->
                        <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,15">
                            <TextBlock Text="成品编号" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <TextBox Text="{Binding PlanModel.ProductCode}" 
                                        IsReadOnly="True"
                                        BorderThickness="0" 
                                        Padding="10,8" 
                                        FontSize="14"
                                        materialDesign:HintAssist.Hint="请输入"
                                        Foreground="#606266"/>
                            </Border>
                        </StackPanel>

                        <!-- 规格型号 -->
                        <StackPanel Grid.Row="3" Grid.Column="0" Margin="0,0,10,15">
                            <TextBlock Text="规格型号" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <TextBox Text="{Binding PlanModel.Specification}" 
                                        IsReadOnly="True"
                                        BorderThickness="0" 
                                        Padding="10,8" 
                                        FontSize="14"
                                        materialDesign:HintAssist.Hint="请输入"
                                        Foreground="#606266"/>
                            </Border>
                        </StackPanel>
                        
                        <!-- 成品类型 -->
                        <StackPanel Grid.Row="3" Grid.Column="1" Margin="10,0,0,15">
                            <TextBlock Text="成品类型" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <TextBox Text="{Binding PlanModel.ProductType}" 
                                        IsReadOnly="True"
                                        BorderThickness="0" 
                                        Padding="10,8" 
                                        FontSize="14"
                                        materialDesign:HintAssist.Hint="请输入"
                                        Foreground="#606266"/>
                            </Border>
                        </StackPanel>

                        <!-- 单位 -->
                        <StackPanel Grid.Row="4" Grid.Column="0" Margin="0,0,10,15">
                            <TextBlock Text="单位" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <TextBox Text="{Binding PlanModel.Unit}" 
                                        IsReadOnly="True"
                                        BorderThickness="0" 
                                        Padding="10,8" 
                                        FontSize="14"
                                        materialDesign:HintAssist.Hint="请输入"
                                        Foreground="#606266"/>
                            </Border>
                        </StackPanel>
                        
                        <!-- 计划数量 -->
                        <StackPanel Grid.Row="4" Grid.Column="1" Margin="10,0,0,15">
                            <TextBlock Text="计划数量" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <TextBox Text="{Binding PlanModel.PlanNumber}" 
                                        BorderThickness="0" 
                                        Padding="10,8" 
                                        FontSize="14"
                                        materialDesign:HintAssist.Hint="请输入"
                                        Foreground="#606266"/>
                            </Border>
                        </StackPanel>

                        <!-- 开工时间 -->
                        <StackPanel Grid.Row="5" Grid.Column="0" Margin="0,0,10,15">
                            <TextBlock Text="开工时间" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <DatePicker SelectedDate="{Binding PlanModel.PlanStartTime}"
                                        BorderThickness="0"
                                        Padding="8,6"
                                        FontSize="14"
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="请选择日期"/>
                            </Border>
                        </StackPanel>
                        
                        <!-- 完工时间 -->
                        <StackPanel Grid.Row="5" Grid.Column="1" Margin="10,0,0,15">
                            <TextBlock Text="完工时间" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <DatePicker SelectedDate="{Binding PlanModel.PlanEndTime}"
                                        BorderThickness="0"
                                        Padding="8,6"
                                        FontSize="14"
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="请选择日期"/>
                            </Border>
                        </StackPanel>
                        
                        <!-- 需求日期 -->
                        <StackPanel Grid.Row="6" Grid.Column="0" Margin="0,0,10,15">
                            <TextBlock Text="需求日期" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <DatePicker SelectedDate="{Binding PlanModel.DemandTime}"
                                        BorderThickness="0"
                                        Padding="8,6"
                                        FontSize="14"
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="请选择日期"/>
                            </Border>
                        </StackPanel>
                        
                        <!-- 备注 -->
                        <StackPanel Grid.Row="7" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,15">
                            <TextBlock Text="备注" Foreground="#303133" FontSize="14" Margin="0,0,0,5"/>
                            <Border BorderThickness="1" BorderBrush="#E8E8E8" CornerRadius="2">
                                <TextBox Text="{Binding PlanModel.PlanRemark}" 
                                        BorderThickness="0" 
                                        Padding="10,8" 
                                        FontSize="14" 
                                        TextWrapping="Wrap"
                                        AcceptsReturn="True"
                                        Height="80"
                                        VerticalScrollBarVisibility="Auto"
                                        materialDesign:HintAssist.Hint="请输入"
                                        Foreground="#606266"/>
                            </Border>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 附件上传区域，上传后赋值Plan.PlanAttachment，并显示已选文件名 -->
                <GroupBox Header="附件" Margin="0,0,0,20">
                    <StackPanel>
                        <Button Content="上传附件" Width="120" Margin="0,10,0,10" Click="UploadAttachment_Click"/>
                        <TextBlock Text="{Binding Plan.PlanAttachment, StringFormat=已选附件: {0}}" Margin="5,0"/>
                        <ItemsControl ItemsSource="{Binding Attachments}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}" Margin="5,0"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        <TextBlock Text="支持 docx, xls, pdf, rar, zip, png, jpg 类型文件" HorizontalAlignment="Center" FontSize="12" Foreground="Gray"/>
                    </StackPanel>
                </GroupBox>

                <!-- BOM组成区域 -->
                <Border Background="White" BorderThickness="0" CornerRadius="4" Padding="0,0,0,20">
                    <StackPanel>
                        <Button Content="BOM组成" Background="Transparent" BorderThickness="0" HorizontalAlignment="Center" 
                                Foreground="#3080FE" Padding="20,15" FontSize="16" FontWeight="Bold"/>
                        
                        <!-- 暂无数据显示 -->
                        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" Margin="0,30,0,20">
                            <materialDesign:PackIcon Kind="FileDocumentOutline" Width="60" Height="60" HorizontalAlignment="Center" Foreground="#DDDDDD"/>
                            <TextBlock Text="暂无数据" Foreground="#999999" Margin="0,10,0,0" HorizontalAlignment="Center"/>
                        </StackPanel>
                        
                        <!-- 选择BOM按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                            <Button Content="选择BOM" Command="{Binding SelectBomCommand}" Background="#3080FE" BorderThickness="0" Padding="20,5" 
                                    Foreground="White" Height="32">
                                <Button.Resources>
                                    <Style TargetType="Border">
                                        <Setter Property="CornerRadius" Value="4"/>
                                    </Style>
                                </Button.Resources>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 返回和保存按钮 -->
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,20,20,0">
            <Button Command="{Binding CancelCommand}" Style="{StaticResource MaterialDesignOutlinedButton}" 
                    Content="取消" Margin="0,0,10,0" Width="80" Height="32"/>
            <Button Command="{Binding SavePlanCommand}" Background="#3080FE" Foreground="White" 
                    Content="保存" Width="80" Height="32">
                <Button.Resources>
                    <Style TargetType="Border">
                        <Setter Property="CornerRadius" Value="4"/>
                    </Style>
                </Button.Resources>
            </Button>
        </StackPanel>
    </Grid>
</UserControl> 