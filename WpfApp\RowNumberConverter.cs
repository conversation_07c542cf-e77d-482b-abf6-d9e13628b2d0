using System;
using System.Globalization;
using System.Windows.Controls;
using System.Windows.Data;

namespace WpfApp
{
    /// <summary>
    /// 行号转换器，用于DataGrid显示序号
    /// </summary>
    public class RowNumberConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 如果直接使用DataGridRow的AlternationIndex，有时会出现不显示的情况
            // 改为使用DataGridRow的GetIndex方法获取更可靠的索引
            if (value is int rowIndex)
            {
                return (rowIndex + 1).ToString();
            }
            
            return "?";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 