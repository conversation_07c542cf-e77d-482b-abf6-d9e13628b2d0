<UserControl x:Class="WpfApp.Plan.AddPlanDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WpfApp.Plan"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             Width="800" Height="700">
    <Border Background="White" BorderBrush="#EEEEEE" BorderThickness="1" CornerRadius="4">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 -->
            <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#EEEEEE" BorderThickness="0,0,0,1" Padding="20,15">
                <TextBlock Text="新增计划" FontSize="18" FontWeight="Bold"/>
            </Border>
            
            <!-- 表单内容 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="20">
                    
                    <!-- 基础信息部分 -->
                    <Border Background="#409EFF" CornerRadius="15" Padding="10,5" Margin="0,0,0,20">
                        <TextBlock Text="基础信息" Foreground="White" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                    </Border>
                    
                    <Grid Margin="0,0,0,30">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 左列 -->
                        <!-- 计划编号 -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal" Margin="0,0,20,5">
                            <TextBlock Text="计划编号" Foreground="#303133" FontSize="14"/>
                            <TextBlock Text="*" Foreground="Red" FontSize="14" Margin="5,0,0,0"/>
                        </StackPanel>
                        <Border Grid.Row="1" Grid.Column="0" Margin="0,0,20,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <TextBox Text="{Binding NewPlan.PlanCode}" 
                                     BorderThickness="0" 
                                     Padding="10,8" 
                                     FontSize="14" 
                                     materialDesign:HintAssist.Hint="请输入"
                                     Foreground="#606266"/>
                        </Border>
                        
                        <!-- 来源类型 -->
                        <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal" Margin="0,0,20,5">
                            <TextBlock Text="来源类型" Foreground="#303133" FontSize="14"/>
                            <TextBlock Text="*" Foreground="Red" FontSize="14" Margin="5,0,0,0"/>
                        </StackPanel>
                        <Border Grid.Row="3" Grid.Column="0" Margin="0,0,20,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <ComboBox ItemsSource="{Binding SourceTypes}" 
                                      SelectedItem="{Binding SelectedSourceType}"
                                      DisplayMemberPath="Name"
                                      BorderThickness="0" 
                                      Padding="8,6" 
                                      FontSize="14" 
                                      Foreground="#606266"
                                      materialDesign:HintAssist.Hint="请选择来源类型">
                                <ComboBox.Resources>
                                    <SolidColorBrush x:Key="MaterialDesignPaper" Color="White"/>
                                </ComboBox.Resources>
                            </ComboBox>
                        </Border>
                        
                        <!-- 成品名称 -->
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="成品名称" Margin="0,0,20,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="5" Grid.Column="0" Margin="0,0,20,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <Grid>
                                <TextBox Text="{Binding NewPlan.ProductName}" 
                                         BorderThickness="0" 
                                         Padding="10,8" 
                                         FontSize="14" 
                                         materialDesign:HintAssist.Hint="请选择成品"
                                         Foreground="#606266"/>
                                <Button HorizontalAlignment="Right" Width="30" Height="30" Padding="0" Margin="0,0,5,0"
                                        Command="{Binding SelectNewProductCommand}"
                                        Background="Transparent" BorderThickness="0">
                                    <materialDesign:PackIcon Kind="ViewGrid" Foreground="#409EFF" Width="16" Height="16"/>
                                </Button>
                            </Grid>
                        </Border>
                        
                        <!-- 规格型号 -->
                        <TextBlock Grid.Row="6" Grid.Column="0" Text="规格型号" Margin="0,0,20,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="7" Grid.Column="0" Margin="0,0,20,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <TextBox Text="{Binding NewPlan.Specification}" 
                                     BorderThickness="0" 
                                     Padding="10,8" 
                                     FontSize="14" 
                                     materialDesign:HintAssist.Hint="请输入"
                                     Foreground="#606266"/>
                        </Border>
                        
                        <!-- 单位 -->
                        <TextBlock Grid.Row="8" Grid.Column="0" Text="单位" Margin="0,0,20,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="9" Grid.Column="0" Margin="0,0,20,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <TextBox Text="{Binding NewPlan.Unit}" 
                                     BorderThickness="0" 
                                     Padding="10,8" 
                                     FontSize="14" 
                                     materialDesign:HintAssist.Hint="请输入"
                                     Foreground="#606266"/>
                        </Border>
                        
                        <!-- 开工时间 -->
                        <TextBlock Grid.Row="10" Grid.Column="0" Text="开工时间" Margin="0,0,20,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="11" Grid.Column="0" Margin="0,0,20,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <DatePicker x:Name="startDatePicker" SelectedDate="{Binding NewPlan.PlanStartTime}"
                                        BorderThickness="0"
                                        Padding="8,6"
                                        FontSize="14"
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="请选择日期"/>
                        </Border>
                        
                        <!-- 需求日期 -->
                        <TextBlock Grid.Row="12" Grid.Column="0" Text="需求日期" Margin="0,0,20,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="13" Grid.Column="0" Margin="0,0,20,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <DatePicker x:Name="demandDatePicker" SelectedDate="{Binding NewPlan.DemandTime}"
                                        BorderThickness="0"
                                        Padding="8,6"
                                        FontSize="14"
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="请选择日期"/>
                        </Border>
                        
                        <!-- 备注 -->
                        <TextBlock Grid.Row="14" Grid.Column="0" Text="备注" Margin="0,0,20,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="15" Grid.Column="0" Margin="0,0,20,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <TextBox Text="{Binding NewPlan.PlanRemark}" 
                                     BorderThickness="0" 
                                     Padding="10,8" 
                                     FontSize="14" 
                                     Height="80"
                                     TextWrapping="Wrap"
                                     AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"
                                     materialDesign:HintAssist.Hint="请输入"
                                     Foreground="#606266"/>
                        </Border>
                        
                        <!-- 右列 -->
                        <!-- 计划名称 -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,5">
                            <TextBlock Text="计划名称" Foreground="#303133" FontSize="14"/>
                            <TextBlock Text="*" Foreground="Red" FontSize="14" Margin="5,0,0,0"/>
                        </StackPanel>
                        <Border Grid.Row="1" Grid.Column="1" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <TextBox Text="{Binding NewPlan.PlanName}" 
                                     BorderThickness="0" 
                                     Padding="10,8" 
                                     FontSize="14" 
                                     materialDesign:HintAssist.Hint="请输入"
                                     Foreground="#606266"/>
                        </Border>
                        
                        <!-- 订单编号 -->
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="订单编号" Margin="0,0,0,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="3" Grid.Column="1" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <Grid>
                                <TextBox Text="{Binding NewPlan.OrderId}" 
                                         BorderThickness="0" 
                                         Padding="10,8" 
                                         FontSize="14" 
                                         materialDesign:HintAssist.Hint="请选择销售单"
                                         Foreground="#606266"/>
                                <Button HorizontalAlignment="Right" Width="30" Height="30" Padding="0" Margin="0,0,5,0"
                                        Command="{Binding SelectOrderCommand}"
                                        Background="Transparent" BorderThickness="0">
                                    <materialDesign:PackIcon Kind="ViewGrid" Foreground="#409EFF" Width="16" Height="16"/>
                                </Button>
                            </Grid>
                        </Border>
                        
                        <!-- 成品编号 -->
                        <TextBlock Grid.Row="4" Grid.Column="1" Text="成品编号" Margin="0,0,0,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="5" Grid.Column="1" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <TextBox Text="{Binding NewPlan.ProductCode}" 
                                     BorderThickness="0" 
                                     Padding="10,8" 
                                     FontSize="14" 
                                     materialDesign:HintAssist.Hint="请输入"
                                     Foreground="#606266"/>
                        </Border>
                        
                        <!-- 成品类型 -->
                        <TextBlock Grid.Row="6" Grid.Column="1" Text="成品类型" Margin="0,0,0,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="7" Grid.Column="1" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <TextBox Text="{Binding NewPlan.ProductType}" 
                                     BorderThickness="0" 
                                     Padding="10,8" 
                                     FontSize="14" 
                                     materialDesign:HintAssist.Hint="请输入"
                                     Foreground="#606266"/>
                        </Border>
                        
                        <!-- 计划数量 -->
                        <TextBlock Grid.Row="8" Grid.Column="1" Text="计划数量" Margin="0,0,0,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="9" Grid.Column="1" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <TextBox Text="{Binding NewPlan.PlanNumber}" 
                                     BorderThickness="0" 
                                     Padding="10,8" 
                                     FontSize="14" 
                                     materialDesign:HintAssist.Hint="请输入"
                                     Foreground="#606266"/>
                        </Border>
                        
                        <!-- 完工时间 -->
                        <TextBlock Grid.Row="10" Grid.Column="1" Text="完工时间" Margin="0,0,0,5" Foreground="#303133" FontSize="14"/>
                        <Border Grid.Row="11" Grid.Column="1" Margin="0,0,0,15" BorderThickness="1" BorderBrush="#DCDFE6" CornerRadius="2">
                            <DatePicker x:Name="endDatePicker" SelectedDate="{Binding NewPlan.PlanEndTime}"
                                        BorderThickness="0"
                                        Padding="8,6"
                                        FontSize="14"
                                        Foreground="#606266"
                                        materialDesign:HintAssist.Hint="请选择日期"/>
                        </Border>

                        <!-- 附件上传区域，上传后赋值NewPlan.PlanAttachment，并显示已选文件名 -->
                        <GroupBox Header="附件" Margin="0,0,0,20" Grid.Row="12" Grid.Column="1" Grid.RowSpan="2">
                            <StackPanel>
                                <Button Content="上传附件" Width="120" Margin="0,10,0,10" Click="UploadAttachment_Click"/>
                                <TextBlock Text="{Binding NewPlan.PlanAttachment, StringFormat=已选附件: {0}}" Margin="5,0"/>
                                <ItemsControl ItemsSource="{Binding Attachments}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding}" Margin="5,0"/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                                <TextBlock Text="支持 docx, xls, pdf, rar, zip, png, jpg 类型文件" HorizontalAlignment="Center" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                        </GroupBox>
                    </Grid>
                    
                    <!-- BOM组成部分 -->
                    <Border Background="#409EFF" CornerRadius="15" Padding="10,5" Margin="0,0,0,20">
                        <TextBlock Text="BOM组成" Foreground="White" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                    </Border>
                    
                    <Border Background="#F8F9FA" BorderBrush="#E8E8E8" BorderThickness="1" CornerRadius="2" Padding="40" Margin="0,0,0,20">
                        <StackPanel HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="PackageVariant" 
                                                     Foreground="#909399" 
                                                     Width="64" 
                                                     Height="64" 
                                                     HorizontalAlignment="Center"
                                                     Margin="0,0,0,10"/>
                            <TextBlock Text="暂无数据" 
                                       Foreground="#909399" 
                                       FontSize="16" 
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,20"/>
                            <Button Content="选择BOM" 
                                    Command="{Binding SelectBomCommand}"
                                    Background="#409EFF" 
                                    Foreground="White"
                                    BorderThickness="0"
                                    Width="120" 
                                    Height="36"
                                    FontSize="14">
                                <Button.Resources>
                                    <Style TargetType="Border">
                                        <Setter Property="CornerRadius" Value="3"/>
                                    </Style>
                                </Button.Resources>
                            </Button>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
            
            <!-- 按钮栏 -->
            <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#EEEEEE" BorderThickness="0,1,0,0" Padding="20,15">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="取消" Command="{Binding CancelAddPlanCommand}"
                            Background="White" Foreground="#606266"
                            BorderBrush="#DCDFE6" BorderThickness="1"
                            Margin="0,0,15,0" Width="80" Height="32">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="3"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                    <Button Content="保存" Command="{Binding SaveNewPlanCommand}"
                            Background="#409EFF" Foreground="White"
                            BorderThickness="0"
                            Width="80" Height="32">
                        <Button.Resources>
                            <Style TargetType="Border">
                                <Setter Property="CornerRadius" Value="3"/>
                            </Style>
                        </Button.Resources>
                    </Button>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</UserControl> 