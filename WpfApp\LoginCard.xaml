<UserControl x:Class="WpfApp.LoginCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:WpfApp"
             Height="450" Width="400">
    <materialDesign:Card
        Width="400"
        Height="450"
        Margin="20"
        VerticalAlignment="Center"
        HorizontalAlignment="Center">
        <StackPanel Margin="30">
            <TextBlock
                Text="用户登录"
                FontSize="24"
                FontWeight="Bold"
                HorizontalAlignment="Center"
                Margin="0,0,0,30"/>
            <StackPanel Margin="0,0,0,20">
                <TextBlock Text="账号" FontSize="14" Margin="0,0,0,5" Foreground="#666"/>
                <TextBox x:Name="UsernameTextBox" materialDesign:HintAssist.Hint="请输入用户名" FontSize="16" Height="50" Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"/>
            </StackPanel>
            <StackPanel Margin="0,0,0,30">
                <TextBlock Text="密码" FontSize="14" Margin="0,0,0,5" Foreground="#666"/>
                <PasswordBox x:Name="PasswordBox" materialDesign:HintAssist.Hint="请输入密码" FontSize="16" Height="50" local:PasswordBoxHelper.BindPassword="True" local:PasswordBoxHelper.BoundPassword="{Binding Password, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"/>
            </StackPanel>
            <Button Content="登录" Height="40" FontSize="16" Background="#673AB7" Foreground="White" BorderThickness="0" Cursor="Hand" Command="{Binding LoginCommand}" />
        </StackPanel>
    </materialDesign:Card>
</UserControl>