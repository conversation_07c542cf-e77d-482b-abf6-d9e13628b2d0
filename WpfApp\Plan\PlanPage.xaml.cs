using System.Windows.Controls;
using System.Windows;
using WpfApp.Plan;
using System;

namespace WpfApp
{
    /// <summary>
    /// PlanPage.xaml 的交互逻辑
    /// </summary>
    public partial class PlanPage : UserControl
    {
        // 页面切换事件
        public event Action<UserControl> SwitchPage = delegate { };  // 初始化为空委托，避免为null
        private PlanViewModel _viewModel;

        public PlanPage()
        {
            InitializeComponent();
            _viewModel = new PlanViewModel();
            DataContext = _viewModel;
            
            // 添加行加载事件处理程序，设置行号
            PlansDataGrid.LoadingRow += PlansDataGrid_LoadingRow;
            // 添加行卸载事件处理程序，清除行号
            PlansDataGrid.UnloadingRow += PlansDataGrid_UnloadingRow;
            // 添加复选框变化事件处理
            PlansDataGrid.LoadingRow += PlansDataGrid_LoadingRow_CheckBox;
            
            // 订阅ViewModel的导航事件
            _viewModel.NavigateToPage += HandleNavigateToPage;
        }
        
        // 处理导航请求
        private void HandleNavigateToPage(UserControl page)
        {
            // 触发页面切换事件
            SwitchPage?.Invoke(page);
        }
        
        private void PlansDataGrid_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            // 检查行是否有数据项
            if (e.Row.Item != null && e.Row.Item is PlanModel)
            {
                // 设置行的Tag属性为行号（从1开始）
                e.Row.Tag = (e.Row.GetIndex() + 1).ToString();
            }
            else
            {
                // 如果没有数据项，不显示序号
                e.Row.Tag = string.Empty;
            }
        }
        
        private void PlansDataGrid_UnloadingRow(object sender, DataGridRowEventArgs e)
        {
            // 清除行的Tag属性
            e.Row.Tag = null;
        }
        
        private void PlansDataGrid_LoadingRow_CheckBox(object sender, DataGridRowEventArgs e)
        {
            // 这个方法现在不需要了，我们使用更简单的方法
        }

        private void PlansDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateSelectedPlans();
        }
        
        private void UpdateSelectedPlans()
        {
            if (_viewModel != null && PlansDataGrid != null)
            {
                System.Diagnostics.Debug.WriteLine("开始更新选中计划");
                
                // 更新ViewModel中的选中项集合
                _viewModel.SelectedPlans.Clear();
                
                // 遍历所有计划，检查IsSelected状态
                int selectedCount = 0;
                foreach (var item in PlansDataGrid.Items)
                {
                    if (item is PlanModel plan)
                    {
                        System.Diagnostics.Debug.WriteLine($"计划 {plan.PlanCode} 的IsSelected状态: {plan.IsSelected}");
                        if (plan.IsSelected)
                        {
                            _viewModel.SelectedPlans.Add(plan);
                            selectedCount++;
                        }
                    }
                }
                
                // 设置单个选中项（用于编辑功能）
                if (PlansDataGrid.SelectedItem is PlanModel selectedPlan)
                {
                    _viewModel.SelectedPlan = selectedPlan;
                }
                else
                {
                    _viewModel.SelectedPlan = null;
                }
                
                // 更新全选状态
                _viewModel.UpdateAllSelectedState();
                
                System.Diagnostics.Debug.WriteLine($"更新选中计划完成，当前选中数量: {_viewModel.SelectedPlans.Count}, 遍历到的选中数量: {selectedCount}");
            }
        }

        private void AddPlanDialog_Loaded(object sender, RoutedEventArgs e)
        {

        }
        
        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("CheckBox_Checked 事件被触发");
            UpdateSelectedPlans();
        }
        
        private void CheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine("CheckBox_Unchecked 事件被触发");
            UpdateSelectedPlans();
        }
    }
} 