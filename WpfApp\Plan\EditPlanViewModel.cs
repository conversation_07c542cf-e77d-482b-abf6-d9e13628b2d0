using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace WpfApp.Plan
{
    public class EditPlanViewModel : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly Action<bool> _goBackAction; // 返回到列表页并可选择是否刷新数据
        private readonly Action _backAction; // 简化的返回操作
        private bool _isLoading;
        private List<SelectOption> _sourceTypes;
        private SelectOption _selectedSourceType;

        public PlanModel PlanModel { get; set; }
        public bool IsNewPlan { get; set; }
        
        public bool IsLoading 
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                // 如果有PropertyChanged事件，这里需要通知UI
            }
        }
        
        public List<SelectOption> SourceTypes
        {
            get => _sourceTypes;
            set
            {
                _sourceTypes = value;
                // 如果有PropertyChanged事件，这里需要通知UI
            }
        }
        
        public SelectOption SelectedSourceType
        {
            get => _selectedSourceType;
            set
            {
                _selectedSourceType = value;
                if (PlanModel != null && value != null)
                {
                    PlanModel.SourceId = value.Id;
                    PlanModel.SourceName = value.Name;
                }
                // 如果有PropertyChanged事件，这里需要通知UI
            }
        }
        
        // 命令
        public ICommand SavePlanCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        public ICommand BackCommand { get; private set; }
        public ICommand SelectProductCommand { get; private set; }
        public ICommand SelectOrderCommand { get; private set; }
        public ICommand SelectBomCommand { get; private set; }
        public ICommand UploadAttachmentCommand { get; private set; }
        
        // 原有构造函数 - 支持传递返回并刷新的操作
        public EditPlanViewModel(Action<bool> goBackAction, PlanModel planModel = null)
        {
            _httpClient = new HttpClient();
            // 不设置BaseAddress，使用完整URL
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("text/plain"));
            
            // 添加认证头
            if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
            }
            
            // 添加请求来源标识
            _httpClient.DefaultRequestHeaders.Add("request-from", "swagger");
            
            _goBackAction = goBackAction;
            
            IsNewPlan = planModel == null;
            PlanModel = planModel ?? new PlanModel
            {
                PlanStartTime = DateTime.Now,
                PlanEndTime = DateTime.Now.AddDays(7),
                DemandTime = DateTime.Now.AddDays(10),
                PlanCode = GeneratePlanCode(),
                PlanStatus = 0,
                PlanStatusName = "未下达"
            };
            
            InitializeCommands();
            InitializeOptions();
        }
        
        // 新构造函数 - 支持简单的返回操作
        public EditPlanViewModel(bool isNewPlan = true, Action backAction = null)
        {
            _httpClient = new HttpClient();
            // 不设置BaseAddress，使用完整URL
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("text/plain"));
            
            // 添加认证头
            if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
            }
            
            // 添加请求来源标识
            _httpClient.DefaultRequestHeaders.Add("request-from", "swagger");
            
            _backAction = backAction;
            IsNewPlan = isNewPlan;
            
            PlanModel = new PlanModel
            {
                PlanStartTime = DateTime.Now,
                PlanEndTime = DateTime.Now.AddDays(7),
                DemandTime = DateTime.Now.AddDays(10),
                PlanCode = GeneratePlanCode(),
                PlanStatus = 0,
                PlanStatusName = "未下达"
            };
            
            InitializeCommands();
            InitializeOptions();
        }
        
        private void InitializeCommands()
        {
            SavePlanCommand = new RelayCommand(SavePlan);
            CancelCommand = new RelayCommand(Cancel);
            BackCommand = new RelayCommand(GoBack);
            SelectProductCommand = new RelayCommand(SelectProduct);
            SelectOrderCommand = new RelayCommand(SelectOrder);
            SelectBomCommand = new RelayCommand(SelectBom);
            UploadAttachmentCommand = new RelayCommand(UploadAttachment);
        }
        
        private async void InitializeOptions()
        {
            // 从API获取来源类型数据
            await LoadSourceTypesFromApi();
        }
        
        private async Task LoadSourceTypesFromApi()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始调用API获取来源类型数据 ===");
                
                // 确保SourceTypes不为null
                if (SourceTypes == null)
                {
                    SourceTypes = new List<SelectOption>();
                    System.Diagnostics.Debug.WriteLine("SourceTypes为null，已创建新实例");
                }
                
                // 调用API获取来源类型列表
                var response = await _httpClient.GetAsync("http://localhost:5005/api/productionPlan/sourceTypeList");
                
                System.Diagnostics.Debug.WriteLine($"API响应状态码: {response.StatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"API响应内容: {json}");
                    
                    var result = JsonSerializer.Deserialize<SourceTypeResponse>(json);
                    
                    if (result?.Result != null)
                    {
                        // 清空现有数据
                        SourceTypes.Clear();
                        
                        // 添加从API获取的数据
                        foreach (var sourceType in result.Result)
                        {
                            if (sourceType != null && !string.IsNullOrEmpty(sourceType.SourceName))
                            {
                                SourceTypes.Add(new SelectOption 
                                { 
                                    Id = sourceType.Id ?? "", 
                                    Name = sourceType.SourceName 
                                });
                                System.Diagnostics.Debug.WriteLine($"添加来源类型: {sourceType.SourceName} (ID: {sourceType.Id})");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"跳过无效的来源类型数据: {sourceType}");
                            }
                        }
                        
                        // 设置默认选中项
                        if (SourceTypes.Count > 0)
                        {
                            SelectedSourceType = SourceTypes[0];
                            
                            // 确保PlanModel的SourceId被设置
                            if (PlanModel != null)
                            {
                                PlanModel.SourceId = SelectedSourceType.Id;
                                PlanModel.SourceName = SelectedSourceType.Name;
                            }
                            
                            System.Diagnostics.Debug.WriteLine($"设置默认选中项: {SelectedSourceType.Name}");
                        }
                        
                        System.Diagnostics.Debug.WriteLine($"=== 成功从API加载来源类型数据，共 {SourceTypes.Count} 条 ===");
                        MessageBox.Show($"成功从API加载了 {SourceTypes.Count} 个来源类型", "API调用成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        return; // 成功加载，直接返回
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("API响应中没有Result数据");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"获取来源类型失败: {response.StatusCode}");
                    var errorContent = await response.Content.ReadAsStringAsync();
                    System.Diagnostics.Debug.WriteLine($"错误响应内容: {errorContent}");
                    MessageBox.Show($"API调用失败，状态码: {response.StatusCode}", "API调用失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                
                // 如果API调用失败，使用默认数据
                System.Diagnostics.Debug.WriteLine("=== 使用默认来源类型数据 ===");
                InitializeDefaultSourceTypes();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取来源类型时发生错误: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"API调用异常: {ex.Message}", "API调用异常", MessageBoxButton.OK, MessageBoxImage.Error);
                // 如果发生异常，使用默认数据
                InitializeDefaultSourceTypes();
            }
        }
        
        private void InitializeDefaultSourceTypes()
        {
            try
            {
                // 确保SourceTypes不为null
                if (SourceTypes == null)
                {
                    SourceTypes = new List<SelectOption>();
                }
                
                // 清空现有数据
                SourceTypes.Clear();
                
                // 添加默认的来源类型选项
                SourceTypes.Add(new SelectOption { Id = "700723681652823", Name = "销售订单" });
                SourceTypes.Add(new SelectOption { Id = "700723681653476", Name = "库存备货" });
                
                // 设置默认选中项
                if (SourceTypes.Count > 0)
                {
                    SelectedSourceType = SourceTypes[0];
                    
                    // 确保PlanModel的SourceId被设置
                    if (PlanModel != null)
                    {
                        PlanModel.SourceId = SelectedSourceType.Id;
                        PlanModel.SourceName = SelectedSourceType.Name;
                    }
                }
                
                System.Diagnostics.Debug.WriteLine("使用默认来源类型数据");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化默认来源类型时发生错误: {ex.Message}");
            }
        }
        
        private string GeneratePlanCode()
        {
            // 生成计划编号：JHBH + 年月日 + 4位随机数
            string dateStr = DateTime.Now.ToString("yyyyMMdd");
            Random random = new Random();
            string randomNum = random.Next(1000, 9999).ToString();
            return $"JHBH{dateStr}{randomNum}";
        }
        
        public async void SavePlan()
        {
            // 验证必填项
            if (string.IsNullOrEmpty(PlanModel.PlanName))
            {
                MessageBox.Show("请输入计划名称", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (string.IsNullOrEmpty(PlanModel.SourceId))
            {
                MessageBox.Show("请选择来源类型", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            if (PlanModel.PlanNumber <= 0)
            {
                MessageBox.Show("请输入有效的计划数量", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            
            IsLoading = true;
            
            try
            {
                // 移除健康检查，直接进行API调用
                
                // 准备请求数据，按照API文档格式
                var requestData = new
                {
                    planName = PlanModel.PlanName ?? "",
                    workOrderNumber = 0, // 默认值
                    sourceId = PlanModel.SourceId ?? "0",
                    productId = PlanModel.ProductId ?? "",
                    planNumber = PlanModel.PlanNumber,
                    planStartTime = PlanModel.PlanStartTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    planEndTime = PlanModel.PlanEndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss"),
                    demandTime = PlanModel.DemandTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.AddDays(7).ToString("yyyy-MM-dd HH:mm:ss"),
                    planRemark = PlanModel.PlanRemark ?? "",
                    planAttachment = PlanModel.PlanAttachment ?? "",
                    bomId = PlanModel.BomId ?? "0",
                    orderId = PlanModel.OrderId ?? "0"
                };
                
                string json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json-patch+json");
                
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"请求URL: http://localhost:5005/api/productionPlan/productionPlan");
                System.Diagnostics.Debug.WriteLine($"请求数据: {json}");
                
                HttpResponseMessage response;
                if (IsNewPlan)
                {
                    // 新增计划
                    response = await _httpClient.PostAsync("http://localhost:5005/api/productionPlan/productionPlan", content);
                    System.Diagnostics.Debug.WriteLine($"响应状态码: {response.StatusCode}");
                    System.Diagnostics.Debug.WriteLine($"响应内容: {await response.Content.ReadAsStringAsync()}");
                }
                else
                {
                    // 更新计划
                    response = await _httpClient.PutAsync($"http://localhost:5005/api/productionPlan/productionPlan/{PlanModel.Id}", content);
                }
                
                if (response.IsSuccessStatusCode)
                {
                    MessageBox.Show("保存成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    if (_goBackAction != null)
                    {
                        _goBackAction(true); // 返回并刷新
                    }
                    else if (_backAction != null)
                    {
                        _backAction(); // 简单返回
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    MessageBox.Show($"保存失败: {response.ReasonPhrase}\n详细信息: {errorContent}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        public void Cancel()
        {
            if (_goBackAction != null)
            {
                _goBackAction(false); // 返回但不刷新
            }
            else if (_backAction != null)
            {
                _backAction(); // 简单返回
            }
        }
        
        public void GoBack()
        {
            Cancel(); // 复用取消逻辑
        }
        
        public void SelectProduct()
        {
            IsLoading = true;

            try
            {
                // 创建产品选择窗口
                var productSelectionWindow = new ProductSelectionWindow();

                // 显示为对话框并获取结果
                if (productSelectionWindow.ShowDialog() == true && productSelectionWindow.SelectedProduct != null)
                {
                    // 保存选择的产品信息
                    var selectedProduct = productSelectionWindow.SelectedProduct;
                    PlanModel.ProductId = selectedProduct.Id;  // 不再需要ToString()转换
                    PlanModel.ProductName = selectedProduct.ProductName;
                    PlanModel.ProductCode = selectedProduct.ProductCode;
                    PlanModel.Specification = selectedProduct.Specification;
                    PlanModel.Unit = selectedProduct.Unit;
                    PlanModel.ProductType = selectedProduct.ProductType;

                    // 清空BOM信息，因为产品变了
                    PlanModel.BomId = null;
                    PlanModel.BomCode = null;
                    PlanModel.BomVersion = null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择产品时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        public void SelectOrder()
        {
            // 实现订单选择功能
            MessageBox.Show("请实现订单选择功能", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        public void SelectBom()
        {
            IsLoading = true;
            
            try
            {
                // 创建BOM选择窗口
                var bomSelectionWindow = new BomSelectionWindow(
                    PlanModel.ProductId,  // 已经是int类型，不需要转换
                    PlanModel.ProductName,
                    PlanModel.ProductCode,
                    PlanModel.Specification,
                    PlanModel.Unit
                );
                
                // 显示为对话框并获取结果
                if (bomSelectionWindow.ShowDialog() == true && bomSelectionWindow.SelectedBom != null)
                {
                    // 保存选择的BOM信息
                    PlanModel.BomId = bomSelectionWindow.SelectedBom.Id;
                    PlanModel.BomCode = bomSelectionWindow.SelectedBom.BomCode;
                    PlanModel.BomVersion = bomSelectionWindow.SelectedBom.BomVersion;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择BOM时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }
        
        public void UploadAttachment()
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择附件文件",
                Filter = "支持的文件类型|*.docx;*.xls;*.xlsx;*.pdf;*.rar;*.zip;*.png;*.jpg;*.jpeg|所有文件|*.*",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                foreach (string fileName in openFileDialog.FileNames)
                {
                    string fileNameOnly = System.IO.Path.GetFileName(fileName);
                    PlanModel.PlanAttachment = fileNameOnly;
                }
            }
        }
        
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
    
} 