using System.Windows;

namespace WpfApp.Plan
{
    /// <summary>
    /// ProductSelectionWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ProductSelectionWindow : Window
    {
        public ProductSelectionViewModel ViewModel { get; private set; }
        public ProductDisplayModel SelectedProduct { get; private set; }

        public ProductSelectionWindow()
        {
            InitializeComponent();
            ViewModel = new ProductSelectionViewModel();
            ViewModel.CloseWindow = (dialogResult, productModel) =>
            {
                if (dialogResult)
                {
                    SelectedProduct = productModel;
                    System.Diagnostics.Debug.WriteLine($"窗口关闭时设置选中产品: {productModel?.ProductName} - {productModel?.ProductCode}");
                }
                DialogResult = dialogResult;
                Close();
            };
            DataContext = ViewModel;
        }
    }
}
