﻿<UserControl x:Class="WpfApp.BomPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <DataGrid ItemsSource="{Binding BomProducts}" AutoGenerateColumns="False" Margin="10">
            <DataGrid.Columns>
                <DataGridTextColumn Header="BOM编号" Binding="{Binding BomCode}" />
                <DataGridCheckBoxColumn Header="系统编号" Binding="{Binding IsSystemCode}" />
                <DataGridCheckBoxColumn Header="默认BOM" Binding="{Binding IsDefaultBom}" />
                <DataGridTextColumn Header="BOM版本" Binding="{Binding BomVersion}" />
                <DataGridTextColumn Header="产品ID" Binding="{Binding ProductId}" />
                <DataGridTextColumn Header="产品编号" Binding="{Binding ProductCode}" />
                <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" />
                <DataGridTextColumn Header="单位" Binding="{Binding Unit}" />
                <DataGridTextColumn Header="日产量" Binding="{Binding DailyOutput}" />
            </DataGrid.Columns>
        </DataGrid>
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,10">
            <Button Content="上一页" Command="{Binding PrevPageCommand}" Margin="5"/>
            <TextBlock Text="{Binding Page}" Margin="10,0"/>
            <Button Content="下一页" Command="{Binding NextPageCommand}" Margin="5"/>
            <TextBlock Text="总数:" Margin="10,0"/>
            <TextBlock Text="{Binding Total}" Margin="5,0"/>
        </StackPanel>
    </Grid>
</UserControl>