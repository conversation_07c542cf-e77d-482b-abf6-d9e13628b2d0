using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System;

namespace WpfApp
{
    public class MainWindowViewModel : INotifyPropertyChanged
    {
        private string _username;
        public string Username
        {
            get => _username;
            set { _username = value; OnPropertyChanged(); }
        }

        private string _password;
        public string Password
        {
            get => _password;
            set { _password = value; OnPropertyChanged(); }
        }

        public ICommand LoginCommand { get; }

        public event Action LoginSucceeded;

        public MainWindowViewModel()
        {
            Username = "admin";
            Password = "123456";
            LoginCommand = new RelayCommand(async _ => await LoginAsync(), _ => !string.IsNullOrWhiteSpace(Username) && !string.IsNullOrWhiteSpace(Password));
        }

        private async Task LoginAsync()
        {
            var httpClient = new HttpClient();
            string url = $"http://localhost:5005/api/sysAuth/simpleLogin/{Username}/{Password}";
            try
            {
                var response = await httpClient.PostAsync(url, null);
                var responseString = await response.Content.ReadAsStringAsync();
                var loginResult = System.Text.Json.JsonSerializer.Deserialize<LoginResponse>(responseString, new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                if (response.IsSuccessStatusCode && loginResult?.Result?.AccessToken != null)
                {
                    AuthContext.Token = loginResult.Result.AccessToken;
                    MessageBox.Show("登录成功", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    LoginSucceeded?.Invoke();
                }
                else
                {
                    MessageBox.Show("登录失败: " + responseString, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (HttpRequestException ex)
            {
                MessageBox.Show("请求失败: " + ex.Message, "网络错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class LoginResponse
    {
        public int Code { get; set; }
        public string Type { get; set; }
        public string Message { get; set; }
        public LoginResultData Result { get; set; }
    }
    public class LoginResultData
    {
        public string AccessToken { get; set; }
        public string RefreshToken { get; set; }
    }

    public class RelayCommand : ICommand
    {
        private readonly Predicate<object> _canExecute;
        private readonly System.Func<object, Task> _execute;
        public RelayCommand(System.Func<object, Task> execute, Predicate<object> canExecute = null)
        {
            _execute = execute;
            _canExecute = canExecute;
        }
        public bool CanExecute(object parameter) => _canExecute == null || _canExecute(parameter);
        public async void Execute(object parameter) => await _execute(parameter);
        public event System.EventHandler CanExecuteChanged
        {
            add => CommandManager.RequerySuggested += value;
            remove => CommandManager.RequerySuggested -= value;
        }
    }
} 