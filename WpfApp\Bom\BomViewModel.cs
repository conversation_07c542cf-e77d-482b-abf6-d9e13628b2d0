using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Collections.Generic; // Added for List<BomProduct>
using WpfApp; // 引用 AuthContext

public class BomViewModel : INotifyPropertyChanged
{
    private int _page = 1;
    private int _pageSize = 11;
    private int _total;
    private ObservableCollection<BomProduct> _bomProducts = new();

    public int Page
    {
        get => _page;
        set { if (_page != value) { _page = value; OnPropertyChanged(nameof(Page)); LoadData(); } }
    }

    public int PageSize
    {
        get => _pageSize;
        set { if (_pageSize != value) { _pageSize = value; OnPropertyChanged(nameof(PageSize)); LoadData(); } }
    }

    public int Total
    {
        get => _total;
        set { _total = value; OnPropertyChanged(nameof(Total)); }
    }

    public ObservableCollection<BomProduct> BomProducts
    {
        get => _bomProducts;
        set { _bomProducts = value; OnPropertyChanged(nameof(BomProducts)); }
    }

    public ICommand PrevPageCommand { get; }
    public ICommand NextPageCommand { get; }

    public BomViewModel()
    {
        PrevPageCommand = new RelayCommand(_ => { if (Page > 1) Page--; });
        NextPageCommand = new RelayCommand(_ => { if (Page * PageSize < Total) Page++; });
        LoadData();
    }

    private async void LoadData()
    {
        using var client = new HttpClient();
        if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
        {
            client.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
        }
        var url = $"http://localhost:5005/api/bom/pageBomEntity?Page={Page}&PageSize={PageSize}";
        var json = await client.GetStringAsync(url);
        var result = System.Text.Json.JsonSerializer.Deserialize<BomPageResponse>(json, new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        BomProducts = new ObservableCollection<BomProduct>(result?.Result?.Items ?? new List<BomProduct>());
        Total = result?.Result?.Total ?? 0;
    }

    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
}

public class BomPageResponse
{
    public int Code { get; set; }
    public string Type { get; set; }
    public string Message { get; set; }
    public BomPageResult Result { get; set; }
}
public class BomPageResult
{
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int Total { get; set; }
    public int TotalPages { get; set; }
    public List<BomProduct> Items { get; set; }
    public bool HasPrevPage { get; set; }
    public bool HasNextPage { get; set; }
} 