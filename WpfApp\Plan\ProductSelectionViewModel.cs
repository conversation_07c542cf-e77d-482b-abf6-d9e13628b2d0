using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace WpfApp.Plan
{
    public class ProductSelectionViewModel : INotifyPropertyChanged
    {
        private readonly HttpClient _httpClient;
        
        private ObservableCollection<ProductDisplayModel> _products = new ObservableCollection<ProductDisplayModel>();
        private bool _isLoading;
        private string _searchKeyword = "";
        private ProductDisplayModel _selectedProduct;
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalCount;
        private int _totalPages;
        private int _goToPage;
        
        // 搜索条件
        private string _selectedUnit = "";
        private string _selectedProductType = "";
        private string _selectedProductAttribute = "";
        
        // 选项列表
        private List<string> _unitOptions = new List<string> { "", "个", "kg", "米", "套", "台", "箱" };
        private List<string> _productTypeOptions = new List<string> { "", "成品", "半成品", "原材料" };
        private List<string> _productAttributeOptions = new List<string> { "", "自制", "外购", "委外" };
        
        private ObservableCollection<PageButton> _pageButtons = new ObservableCollection<PageButton>();
        
        public ObservableCollection<ProductDisplayModel> Products
        {
            get => _products;
            set
            {
                _products = value;
                OnPropertyChanged(nameof(Products));
                OnPropertyChanged(nameof(IsEmpty));
            }
        }
        
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
            }
        }
        
        public string SearchKeyword
        {
            get => _searchKeyword;
            set
            {
                _searchKeyword = value;
                OnPropertyChanged(nameof(SearchKeyword));
            }
        }
        
        public ProductDisplayModel SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                _selectedProduct = value;
                OnPropertyChanged(nameof(SelectedProduct));
            }
        }
        
        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                _currentPage = value;
                OnPropertyChanged(nameof(CurrentPage));
                LoadData();
            }
        }
        
        public int PageSize
        {
            get => _pageSize;
            set
            {
                _pageSize = value;
                OnPropertyChanged(nameof(PageSize));
            }
        }
        
        public int TotalCount
        {
            get => _totalCount;
            set
            {
                _totalCount = value;
                OnPropertyChanged(nameof(TotalCount));
            }
        }
        
        public int TotalPages
        {
            get => _totalPages;
            set
            {
                _totalPages = value;
                OnPropertyChanged(nameof(TotalPages));
                UpdatePageButtons();
            }
        }
        
        public int GoToPage
        {
            get => _goToPage;
            set
            {
                _goToPage = value;
                OnPropertyChanged(nameof(GoToPage));
            }
        }
        
        public string SelectedUnit
        {
            get => _selectedUnit;
            set
            {
                _selectedUnit = value;
                OnPropertyChanged(nameof(SelectedUnit));
            }
        }
        
        public string SelectedProductType
        {
            get => _selectedProductType;
            set
            {
                _selectedProductType = value;
                OnPropertyChanged(nameof(SelectedProductType));
            }
        }
        
        public string SelectedProductAttribute
        {
            get => _selectedProductAttribute;
            set
            {
                _selectedProductAttribute = value;
                OnPropertyChanged(nameof(SelectedProductAttribute));
            }
        }
        
        public List<string> UnitOptions => _unitOptions;
        public List<string> ProductTypeOptions => _productTypeOptions;
        public List<string> ProductAttributeOptions => _productAttributeOptions;
        
        public ObservableCollection<PageButton> PageButtons
        {
            get => _pageButtons;
            set
            {
                _pageButtons = value;
                OnPropertyChanged(nameof(PageButtons));
            }
        }
        
        public bool IsEmpty => Products?.Count == 0 && !IsLoading;
        
        // 命令
        public ICommand SearchCommand { get; private set; }
        public ICommand ResetCommand { get; private set; }
        public ICommand ConfirmCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        public ICommand PrevPageCommand { get; private set; }
        public ICommand NextPageCommand { get; private set; }
        public ICommand GoToPageCommand { get; private set; }
        
        // 关闭窗口的回调
        public Action<bool, ProductDisplayModel> CloseWindow { get; set; }
        
        public ProductSelectionViewModel()
        {
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri("http://localhost:5005/");
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            
            // 添加认证头
            if (!string.IsNullOrEmpty(WpfApp.AuthContext.Token))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + WpfApp.AuthContext.Token);
            }
            
            InitializeCommands();
            LoadData();
        }
        
        private void InitializeCommands()
        {
            SearchCommand = new RelayCommand(Search);
            ResetCommand = new RelayCommand(Reset);
            ConfirmCommand = new RelayCommand(Confirm, CanConfirm);
            CancelCommand = new RelayCommand(Cancel);
            PrevPageCommand = new RelayCommand(PrevPage, CanPrevPage);
            NextPageCommand = new RelayCommand(NextPage, CanNextPage);
            GoToPageCommand = new RelayCommand(GoToPageExecute);
        }
        
        private async void LoadData()
        {
            IsLoading = true;
            Products.Clear();
            
            try
            {
                // 构建查询参数
                var url = $"api/productionPlan/pageProduct?Page={CurrentPage}&PageSize={PageSize}";
                
                // 添加搜索条件
                if (!string.IsNullOrWhiteSpace(SearchKeyword))
                {
                    url += $"&keyword={Uri.EscapeDataString(SearchKeyword)}";
                }
                if (!string.IsNullOrWhiteSpace(SelectedUnit))
                {
                    url += $"&unit={Uri.EscapeDataString(SelectedUnit)}";
                }
                if (!string.IsNullOrWhiteSpace(SelectedProductType))
                {
                    url += $"&productType={Uri.EscapeDataString(SelectedProductType)}";
                }
                if (!string.IsNullOrWhiteSpace(SelectedProductAttribute))
                {
                    url += $"&productAttribute={Uri.EscapeDataString(SelectedProductAttribute)}";
                }
                
                System.Diagnostics.Debug.WriteLine($"Request URL: {url}");
                
                // 发起API请求
                var response = await _httpClient.GetAsync(url);
                var content = await response.Content.ReadAsStringAsync();
                
                System.Diagnostics.Debug.WriteLine($"API Response Status: {response.StatusCode}");
                System.Diagnostics.Debug.WriteLine($"API Response Content: {content}");
                
                if (response.IsSuccessStatusCode)
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        Converters = { new DateTimeConverter(), new NullableDateTimeConverter() }
                    };
                    
                    var apiResponse = JsonSerializer.Deserialize<ApiResponse<PagedResult<ProductModel>>>(content, options);
                    
                    if (apiResponse != null && apiResponse.Code == 200 && apiResponse.Result != null)
                    {
                        var result = apiResponse.Result;
                        TotalCount = result.Total;
                        TotalPages = result.TotalPages;
                        
                        // 转换为显示模型
                        var displayModels = result.Items.Select((product, index) => new ProductDisplayModel
                        {
                            RowNumber = (CurrentPage - 1) * PageSize + index + 1,
                            Model = product
                        }).ToList();
                        
                        Products = new ObservableCollection<ProductDisplayModel>(displayModels);
                        
                        System.Diagnostics.Debug.WriteLine($"Loaded {Products.Count} products");
                        
                        // 添加详细的产品数据调试信息
                        foreach (var product in result.Items)
                        {
                            System.Diagnostics.Debug.WriteLine($"Product: {product.ProductName}, Code: {product.ProductCode}, Spec: '{product.Specification}', Unit: '{product.Unit}', Type: {product.ProductType}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"API response was not successful");
                        MessageBox.Show("获取产品数据失败，请联系系统管理员", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"API request failed: {response.StatusCode} - {content}");
                    MessageBox.Show($"请求失败: {response.StatusCode}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Exception: {ex.Message}");
                MessageBox.Show($"加载数据时发生异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                OnPropertyChanged(nameof(IsEmpty));
            }
        }
        
        private void UpdatePageButtons()
        {
            PageButtons.Clear();
            
            if (TotalPages <= 0) return;
            
            // 最多显示5个页码按钮
            int startPage = Math.Max(1, CurrentPage - 2);
            int endPage = Math.Min(TotalPages, startPage + 4);
            
            // 调整startPage，确保显示5个按钮（如果总页数足够）
            if (endPage - startPage < 4 && startPage > 1)
            {
                startPage = Math.Max(1, endPage - 4);
            }
            
            for (int i = startPage; i <= endPage; i++)
            {
                var pageButton = new PageButton();
                pageButton.PageNumber = i;
                pageButton.IsSelected = i == CurrentPage;
                PageButtons.Add(pageButton);
            }
        }
        
        private void Search()
        {
            CurrentPage = 1;
            LoadData();
        }
        
        private void Reset()
        {
            SearchKeyword = "";
            SelectedUnit = "";
            SelectedProductType = "";
            SelectedProductAttribute = "";
            CurrentPage = 1;
            LoadData();
        }
        
        private bool CanConfirm()
        {
            return SelectedProduct != null;
        }
        
        private void Confirm()
        {
            if (SelectedProduct != null)
            {
                System.Diagnostics.Debug.WriteLine($"确认选择产品: {SelectedProduct.ProductName} - {SelectedProduct.ProductCode}");
                CloseWindow?.Invoke(true, SelectedProduct);
            }
            else
            {
                MessageBox.Show("请选择一个产品", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        
        private void Cancel()
        {
            CloseWindow?.Invoke(false, null);
        }
        
        private void PrevPage()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
            }
        }
        
        private bool CanPrevPage()
        {
            return CurrentPage > 1;
        }
        
        private void NextPage()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
            }
        }
        
        private bool CanNextPage()
        {
            return CurrentPage < TotalPages;
        }
        
        private void GoToPageExecute()
        {
            if (int.TryParse(GoToPage.ToString(), out var page) && page > 0 && page <= TotalPages)
            {
                CurrentPage = page;
            }
        }
        
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    
    // 用于显示在DataGrid中的模型
    public class ProductDisplayModel
    {
        public int RowNumber { get; set; }
        public ProductModel Model { get; set; }
        
        public string Id => Model?.Id;
        public string ProductCode => Model?.ProductCode;
        public string ProductName => Model?.ProductName;
        public string Specification => Model?.Specification;
        public string Unit => Model?.Unit;
        public string ProductType => Model?.ProductType;
        public string ProductAttribute => Model?.ProductAttribute;
        
        // 添加调试信息
        public override string ToString()
        {
            return $"ProductDisplayModel: Name={ProductName}, Code={ProductCode}, Spec={Specification}, Unit={Unit}, Type={ProductType}";
        }
    }
}
