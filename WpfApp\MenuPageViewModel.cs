﻿using System.ComponentModel;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows;
using System;
using WpfApp;
using WpfApp.Plan;

public class MenuPageViewModel : INotifyPropertyChanged
{
    private UserControl _currentView;
    
    // 正确的导航属性
    public UserControl CurrentView
    {
        get => _currentView;
        set
        {
            _currentView = value;
            OnPropertyChanged(nameof(CurrentView));
        }
    }

    public ICommand ShowMaterialCommand { get; set; }
    public ICommand ShowBomCommand { get; set; }
    public ICommand ShowSettingsCommand { get; set; }
    // 添加其他菜单命令
    public ICommand ShowHomeCommand { get; set; }
    public ICommand ShowProductionPlanCommand { get; set; }
    public ICommand ShowWorkOrderCommand { get; set; }
    public ICommand ShowTaskCommand { get; set; }
    public ICommand ShowReportCommand { get; set; }
    public ICommand ShowQualityCommand { get; set; }
    public ICommand ShowProcessRouteCommand { get; set; }
    public ICommand ShowProcedureCommand { get; set; }
    public ICommand ShowWorkshopCommand { get; set; }
    public ICommand ShowMonitoringCommand { get; set; }
    public event Action<string> MenuChanged;
    public event PropertyChangedEventHandler PropertyChanged;

    // 在构造函数中初始化命令
    public MenuPageViewModel()
    {
        // 使用一致的同步命令初始化
        ShowHomeCommand = new RelayCommand(ShowHome);
        ShowProductionPlanCommand = new RelayCommand(ShowProductionPlan);
        ShowWorkOrderCommand = new RelayCommand(ShowWorkOrder);
        ShowTaskCommand = new RelayCommand(ShowTask);
        ShowReportCommand = new RelayCommand(ShowReport);
        ShowQualityCommand = new RelayCommand(ShowQuality);
        ShowProcessRouteCommand = new RelayCommand(ShowProcessRoute);
        ShowProcedureCommand = new RelayCommand(ShowProcedure);
        ShowWorkshopCommand = new RelayCommand(ShowWorkshop);
        ShowMonitoringCommand = new RelayCommand(ShowMonitoring);
        ShowMaterialCommand = new RelayCommand(ShowMaterial);
        ShowBomCommand = new RelayCommand(ShowBom);
    }

    // 实现所有菜单导航方法
    private void ShowHome()
    {
        MenuChanged?.Invoke("首页");
    }

    private void ShowProductionPlan()
    {
        // 导航到生产计划页面
        var planPage = new PlanPage();
        planPage.SwitchPage += HandlePlanPageNavigation;
        CurrentView = planPage;
        MenuChanged?.Invoke("生产计划");
    }

    // 处理计划页面的导航请求
    private void HandlePlanPageNavigation(UserControl page)
    {
        try
        {
            if (page != null)
            {
                // 更新当前视图为新页面
                CurrentView = page;
                // 添加调试信息
                System.Diagnostics.Debug.WriteLine($"已导航到新页面: {page.GetType().Name}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("警告: 尝试导航到空页面");
            }
        }
        catch (Exception ex)
        {
            // 捕获并记录任何导航异常
            System.Diagnostics.Debug.WriteLine($"导航时发生异常: {ex.Message}");
            MessageBox.Show($"导航过程中发生错误: {ex.Message}", "导航错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShowWorkOrder()
    {
        MenuChanged?.Invoke("生产工单");
    }

    private void ShowTask()
    {
        MenuChanged?.Invoke("工单任务");
    }

    private void ShowReport()
    {
        MenuChanged?.Invoke("报工记录");
    }

    private void ShowQuality()
    {
        MenuChanged?.Invoke("质量检验");
    }

    private void ShowProcessRoute()
    {
        MenuChanged?.Invoke("工艺路线");
    }

    private void ShowProcedure()
    {
        MenuChanged?.Invoke("工序管理");
    }

    private void ShowWorkshop()
    {
        MenuChanged?.Invoke("车间管理");
    }

    private void ShowMonitoring()
    {
        MenuChanged?.Invoke("生产监控");
    }

    private void ShowMaterial()
    {
        MenuChanged?.Invoke("物料管理");
    }
    private void ShowBom()
    {
        // 修复属性名称，使用正确的CurrentView
        CurrentView = new BomPage();
        MenuChanged?.Invoke("BOM");
    }

    //private void ShowSettings()
    //{
    //    CurrentView = new SettingsPage();
    //    MenuChanged?.Invoke("Settings");
    //}

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}