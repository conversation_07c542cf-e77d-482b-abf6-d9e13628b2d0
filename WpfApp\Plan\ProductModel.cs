using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace WpfApp.Plan
{
    /// <summary>
    /// 产品数据模型
    /// </summary>
    public class ProductModel
    {
        public string Id { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public string Specification { get; set; }
        public string Unit { get; set; }
        public string ProductType { get; set; }
        public string ProductAttribute { get; set; }
        
        [JsonConverter(typeof(DateTimeConverter))]
        public DateTime CreateTime { get; set; }
        
        public string CreateUserId { get; set; }
        public string CreateUserName { get; set; }
        
        [JsonConverter(typeof(NullableDateTimeConverter))]
        public DateTime? UpdateTime { get; set; }
        
        public string UpdateUserId { get; set; }
        public string UpdateUserName { get; set; }
        public bool IsDelete { get; set; }
    }
}
